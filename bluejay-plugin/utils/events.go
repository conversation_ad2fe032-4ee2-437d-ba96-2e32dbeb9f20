package utils

type WalletEvent struct {
	TransactionID   string `json:"transaction_id,omitempty"`
	TransactionType string `json:"transaction_type,omitempty"` // "source" or "sink"
	Reason          string `json:"reason,omitempty"`           // Reason/source of the transaction (e.g., "purchase", "reward", "gift")
	CurrencyType    string `json:"currency_type,omitempty"`
	CurrencyAmount  int    `json:"currency_amount,omitempty"`
	CurrencyBalance int    `json:"currency_balance,omitempty"`
}

type InAppPurchaseEvent struct {
	EventName     string  `json:"event_name"`
	TransactionID string  `json:"transaction_id,omitempty"`
	ProductID     string  `json:"product_id,omitempty"`
	Price         float64 `json:"price,omitempty"`
	CurrencyCode  string  `json:"currency_code,omitempty"`
}

type EntitlementEvent struct {
	TransactionID string `json:"transaction_id,omitempty"`
	ProductID     string `json:"product_id,omitempty"`
	Source        string `json:"source,omitempty"` // Reason/source of the entitlement (e.g., "purchase", "reward", "gift")
}

type ChallengeEvent struct {
	ID              string `json:"id"`
	ChallengeName   string `json:"challenge_name"`
	Reason          string `json:"reason"`
	CurrentProgress int    `json:"current_progress"`
	MaxProgress     int    `json:"max_progress"`
	Completed       bool   `json:"completed"`
}
